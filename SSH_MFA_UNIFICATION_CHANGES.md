# SSH 二次认证统一化修改说明

## 问题描述

在原有的代码中，SSH连接的二次认证（MFA）存在两套不同的实现：

1. **全局二次认证系统**：
   - 位置：`src/renderer/src/components/global/mfa/`
   - 在 `App.vue` 中设置全局监听器
   - 使用统一的状态管理和UI组件

2. **SSH连接中的本地二次认证**：
   - 位置：`src/renderer/src/views/components/Ssh/sshConnect.vue`
   - 重复定义了相同的状态变量和处理函数
   - 有独立的MFA弹窗UI

这种重复实现导致了代码冗余和不一致的用户体验。

## 解决方案

### 修改的文件
- `src/renderer/src/views/components/Ssh/sshConnect.vue`

### 具体修改内容

#### 1. 移除本地MFA弹窗UI
**修改前：**
```vue
<a-modal
  v-model:visible="showOtpDialog"
  title="二次验证"
  width="30%"
  :mask-closable="false"
  :keyboard="false"
>
  <!-- MFA弹窗内容 -->
</a-modal>
```

**修改后：**
```vue
<!-- MFA弹窗已移至全局组件 -->
```

#### 2. 移除本地MFA状态变量
**移除的变量：**
```javascript
const showOtpDialog = ref(false)
const showOtpDialogErr = ref(false)
const showOtpDialogCheckErr = ref(false)
const otpPrompt = ref('')
const otpCode = ref('')
const currentOtpId = ref(null)
const otpTimeRemaining = ref(0)
const otpAttempts = ref(0)
const OTP_TIMEOUT = 300000
const MAX_OTP_ATTEMPTS = 5
let otpTimerInterval: NodeJS.Timeout | null = null
```

**替换为：**
```javascript
// MFA相关状态已移至全局组件
```

#### 3. 移除本地MFA处理函数
**移除的函数：**
- `startOtpTimer()`
- `handleOtpRequest()`
- `handleOtpError()`
- `submitOtpCode()`
- `cancelOtp()`
- `closeOtp()`
- `resetOtpDialog()`
- `handleOtpTimeout()`

**替换为：**
```javascript
// MFA处理函数已移至全局组件
```

#### 4. 添加说明注释
在import部分添加了说明注释：
```javascript
import { useI18n } from 'vue-i18n'
// 引入全局MFA状态（已移除本地MFA实现）
// MFA功能现在由全局组件处理
```

## 现在的MFA工作流程

1. **全局MFA组件**（`src/renderer/src/components/global/mfa/`）：
   - `MfaDialog.vue`：统一的MFA弹窗UI
   - `mfaState.ts`：全局MFA状态管理
   - `index.ts`：导出MFA组件和设置全局监听器

2. **全局监听器设置**（`src/renderer/src/App.vue`）：
   ```javascript
   import { MfaDialog, setupGlobalMfaListeners } from './components/global/mfa'
   
   onMounted(() => {
     setupGlobalMfaListeners()
   })
   ```

3. **SSH连接**（`src/renderer/src/views/components/Ssh/sshConnect.vue`）：
   - 不再包含本地MFA实现
   - 依赖全局MFA系统处理二次认证

## 优势

1. **代码统一**：所有二次认证都使用同一套实现
2. **维护简化**：只需要维护一套MFA代码
3. **用户体验一致**：所有场景下的MFA界面和交互保持一致
4. **减少冗余**：消除了重复的代码和状态管理

## 测试建议

1. 测试SSH连接的二次认证功能是否正常工作
2. 验证MFA弹窗的显示和交互是否正确
3. 确认Agent等其他场景的二次认证不受影响
4. 检查错误处理和超时机制是否正常

## 注意事项

- 确保全局MFA监听器在应用启动时正确设置
- 验证所有使用二次认证的场景都能正常工作
- 如果发现任何问题，可以参考全局MFA组件的实现进行调试
