export default {
  common: {
    language: 'English',
    workspace: 'WorkSpace',
    files: 'Files',
    keychain: 'Keychain',
    extensions: 'Extensions',
    monitor: 'Monitor',
    ai: 'AI',
    user: 'User',
    setting: 'Setting',
    notice: 'Notice',
    logout: 'Logout',
    login: 'Login',
    userInfo: 'User Info',
    userConfig: 'Setting',
    alias: '<PERSON><PERSON> Config',
    assetConfig: 'Asset Config',
    keyChainConfig: 'Key Chain',
    search: 'Search',
    connect: 'Connect',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    create: 'Create',
    cancel: 'Cancel',
    remove: 'Remove',
    noData: 'No Data',
    close: 'Close',
    closeOther: 'Close Others',
    closeAll: 'Close All',
    copy: 'Copy',
    paste: 'Paste',
    disconnect: 'Disconnect',
    reconnect: 'Reconnect',
    newTerminal: 'New Terminal',
    splitRight: 'Split Right',
    splitDown: 'Split Down',
    clearTerm: 'Clear Screen',
    shrotenName: 'Shorten The Host Name',
    fontsize: 'Fontsize',
    largen: 'Largen',
    smaller: 'Smaller',
    globalExecOn: 'Global Execution (On)',
    globalExec: 'Global Execution',
    syncInputOn: 'Synchronous Input (On)',
    syncInput: 'Synchronous Input',
    allExecuted: 'All Executed',
    pleaseLoginFirst: 'Please login first',
    select: 'Select',
    reset: 'Reset',
    confirm: 'Confirm',
    ok: 'OK',
    quickCommandOn: 'Quick Command (On) ',
    quickCommand: 'Quick Command',
    add: 'Add',
    all: 'All',
    refresh: 'Refresh',
    fullscreen: 'Fullscreen',
    exitFullscreen: 'Exit Fullscreen',
    editFile: 'Edit File:',
    newFile: 'New File:',
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    saveError: 'Save error',
    saveConfirmTitle: 'Save Changes',
    saveConfirmContent: 'Do you want to save changes to {filePath}?',
    pleaseInputLabel: 'Please input label name',
    pleaseInputPrivateKey: 'Please input private key'
  },
  term: {
    welcome: 'Welcome to use Chaterm'
  },
  login: {
    enterprise: 'Enterprise',
    personal: 'Personal',
    contact: 'Contact us',
    welcome: 'Welcome to use',
    title: 'Chaterm',
    loginByUser: 'Login with account',
    loginByEmail: 'Login with email',
    login: 'Login',
    loggingIn: 'Logging in...',
    skip: "Don't want to sign in right now?",
    skipLogin: 'Skip for now',
    applyTag: 'No account ? ',
    apply: 'Apply',
    pleaseInputUsername: 'Please input username',
    pleaseInputPassword: 'Please input password',
    pleaseInputEmail: 'Please input email',
    pleaseInputCode: 'Please input code',
    invalidEmail: 'Please input a valid email address',
    usernameTooltip: 'Please input your username',
    passwordTooltip: 'Please input your password',
    retryAfter: 'Retry after {seconds} seconds',
    getCode: 'Send code'
  },
  workspace: {
    workspace: 'Workspace',
    personal: 'Personal',
    searchHost: 'Search host'
  },
  header: {
    title: 'Smart Bastion Host Management System'
  },
  user: {
    autoCompleteStatus: 'Auto completion',
    quickVimStatus: 'Quick vim',
    commonVimStatus: 'Common vim',
    aliasStatus: 'Global alias',
    highlightStatus: 'Highlight',
    fontSize: 'Font size',
    cursorStyle: 'Cursor type',
    scrollBack: 'Number of scroll rollback lines',
    terminalType: 'Specify terminal type',
    install: 'Installed',
    installing: 'Installing',
    notInstall: 'Not Installed',
    uninstall: 'Uninstall',
    uninstalling: 'Uninstalling',
    baseSetting: 'Base setting',
    terminalSetting: 'Terminal setting',
    ai: 'AI',
    keychain: 'Keychain',
    textEditor: 'Text editor',
    visualVimEditor: 'Visual Vim Editor',
    fileManagerPlugin: 'File manager plugin',
    fileManagerPluginDescribe: 'Open the file manager by right clicking with the mouse',
    cursorStyleBlock: 'Block',
    cursorStyleBar: 'Bar',
    cursorStyleUnderline: 'Underline',
    mouseEvent: 'Mouse Event',
    middleMouseEvent: 'Middle Mouse Event',
    rightMouseEvent: 'Right Mouse Event',
    pasteClipboard: 'Paste Clipboard',
    showContextMenu: 'Show Context Menu',
    none: 'None',
    watermark: 'Watermark',
    watermarkDescribe: 'Show the watermark on the terminal',
    watermarkOpen: 'Open',
    watermarkClose: 'Close',
    language: 'Language',
    theme: 'Theme',
    themeDark: 'Dark',
    themeLight: 'Light',
    telemetry: 'Telemetry',
    telemetryEnabled: 'Enabled',
    telemetryDisabled: 'Disabled',
    telemetryDescription:
      'Help improve Chaterm by sending anonymous usage data and error reports. We never send any code, prompt content, or personal information. For more information, please see our <a href="https://docs.chaterm.ai/user/privacy" target="_blank" rel="noopener noreferrer">privacy policy</a>.',
    enterprise: 'Enterprise',
    personal: 'Personal',
    name: 'Name',
    email: 'Email',
    mobile: 'Mobile',
    organization: 'Organization',
    ip: 'IP Address',
    macAddress: 'Mac Address',
    general: 'General',
    extensions: 'Extensions',
    about: 'About',
    shortcuts: 'Shortcuts',
    shortcutSettings: 'Shortcut Settings',
    shortcutDescription: 'Customize application shortcuts',
    shortcutAction: 'Action',
    shortcutKey: 'Shortcut',
    shortcutModify: 'Modify',
    shortcutReset: 'Reset',
    shortcutSave: 'Save',
    shortcutCancel: 'Cancel',
    shortcutConflict: 'Shortcut Conflict',
    shortcutConflictMessage: 'This shortcut is already in use, please choose another',
    shortcutInvalid: 'Invalid Shortcut',
    shortcutInvalidMessage: 'Please enter a valid shortcut combination',
    shortcutSaveSuccess: 'Shortcut saved successfully',
    shortcutSaveFailed: 'Failed to save shortcut',
    shortcutResetSuccess: 'Shortcut reset successfully',
    shortcutPressKeys: 'Press shortcut keys',
    shortcutRecording: 'Recording...',
    shortcutClickToModify: 'Click to modify',
    model: 'Model',
    enableExtendedThinking: 'Enable extended thinking',
    enableExtendedThinkingDescribe: 'Higher budgets may allow you to achieve more comprehensive and nuanced reasoning',
    autoApproval: 'Auto Approval',
    autoApprovalDescribe: 'Allow Agent to run tools without asking for confirmation',
    features: 'Features',
    enableCheckpoints: 'Enable Checkpoints',
    enableCheckpointsDescribe: 'Enables extension to save checkpoints of workspace throughout the task',
    openAIReasoningEffort: 'OpenAI Reasoning Effort',
    openAIReasoningEffortLow: 'Low',
    openAIReasoningEffortMedium: 'Medium',
    openAIReasoningEffortHigh: 'High',
    proxySettings: 'Proxy Settings',
    enableProxy: 'Enable Proxy',
    proxyType: 'Proxy Protocol',
    proxyHost: 'Host Name',
    proxyPort: 'Port Number',
    enableProxyIdentity: 'Proxy Authentication',
    proxyUsername: 'Username',
    proxyPassword: 'Password',
    shellIntegrationTimeout: 'Shell integration timeout (seconds)',
    shellIntegrationTimeoutPh: 'Enter timeout in seconds',
    shellIntegrationTimeoutDescribe: 'Set how long to wait for shell integration to activate',
    terminal: 'Terminal',
    apiConfiguration: 'API Configuration',
    apiProvider: 'API Provider',
    apiProviderDescribe:
      'Authenticate by either providing the keys above or use the default AWS credential providers, i.e. ~/.aws/credentials or environment variables. These credentials are only used locally to make API requests from this client.',
    awsAccessKey: 'AWS Access Key',
    awsAccessKeyPh: 'Enter Access Key',
    awsSecretKey: 'AWS Secret Key',
    awsSecretKeyPh: 'Enter Secret Key',
    awsSessionToken: 'AWS Session Token',
    awsSessionTokenPh: 'Enter Session Token',
    awsRegion: 'AWS Region',
    awsRegionPh: 'Select a region...',
    awsEndpointSelected: 'Use custom VPC endpoint',
    awsBedrockEndpointPh: 'Enter VPC Endpoint URL (optional)',
    awsUseCrossRegionInference: 'Use cross-region inference',
    chatSettings: 'Chat Mode',
    liteLlmBaseUrl: 'Base URL',
    liteLlmBaseUrlPh: 'Enter Base URL',
    liteLlmApiKey: 'API Key',
    liteLlmApiKeyPh: 'Enter API Key',
    liteLlmApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    customInstructions: 'Custom Instructions',
    customInstructionsPh: `These instructions are added to the end of the system prompt sent with every request. e.g. Always respond in Chinese-simplified`,
    deepSeekApiKey: `DeepSeek API Key`,
    deepSeekApiKeyPh: `Enter API Key...`,
    deepSeekApiKeyDescribe: `This key is stored locally and only used to make API requests from this client.`,
    openAiBaseUrl: 'OpenAI Base URL',
    openAiBaseUrlPh: 'Enter Base URL',
    openAiApiKey: 'OpenAI API Key',
    openAiApiKeyPh: 'Enter API Key',
    openAiApiKeyDescribe: 'This key is stored locally and only used to make API requests from this client.',
    checkModelConfigFailMessage: 'Missing configuration parameters',
    checkModelConfigFailDescription: 'Please fill in the necessary model configuration, Setting -> Models -> Add Model -> API Configuration',
    checkSuccessMessage: 'Connection successful',
    checkSuccessDescription: 'API key is valid',
    checkFailMessage: 'Connection failed',
    checkFailDescriptionDefault: 'unknown error',
    checkFailDescriptionMain: 'Unable to connect to the main process',
    models: 'Models',
    modelNames: 'Model Names',
    aiPreferences: 'AI Preferences',
    addModel: 'Add Model',
    addModelExistError: 'A model with this name already exists',
    addModelSuccess: 'Model added successfully',
    billing: 'Billing Usage',
    subscription: 'Subscription Type',
    expires: 'Expiration Date',
    ratio: 'Usage Ratio',
    budgetResetAt: 'Next Reset Time',
    sshAgentSettings: 'SSH Agent Settiong',
    noKeyAdd: 'No key added yet',
    remove: 'Remove',
    comment: 'Comment',
    fingerprint: 'Fingerprint',
    addSuccess: 'add Success',
    addFailed: 'Add Failed',
    type: 'Type'
  },
  extensions: {
    extensions: 'Extensions',
    alias: 'Alias',
    aliasDescription: 'Global alias config',
    fuzzySearch: 'Fuzzy Search',
    command: 'Command',
    action: 'Action',
    success: 'Success',
    error: 'Error',
    errorDescription: 'Creation failed!',
    errorNetWork: 'Network request exception',
    warning: 'Warning',
    missingAliasCommand: 'Missing alias or command!',
    aliasAlreadyExists: 'Alias already exists！',
    addCommand: 'Add Command'
  },
  shortcuts: {
    actions: {
      openSettings: 'Open Settings',
      toggleLeftSidebar: 'Toggle Left Sidebar',
      toggleRightSidebar: 'Toggle Right Sidebar',
      sendOrToggleAi: 'Send to AI / Toggle AI Sidebar',
      switchToNextTab: 'Next Tab',
      switchToPrevTab: 'Previous Tab',
      switchToSpecificTab: 'Switch to specific Tab[1...9]'
    }
  },
  personal: {
    host: 'Host',
    newHost: 'New Host',
    keyChain: 'KeyChain',
    address: 'Connection Address',
    general: 'General',
    group: 'Group',
    accountPassword: 'Account Password',
    key: 'Key',
    username: 'Username',
    password: 'Password',
    remoteHost: 'Remote host',
    port: 'Port',
    verificationMethod: 'Verification Method',
    alias: 'Alias',
    pleaseInputRemoteHost: 'Please input remote host',
    pleaseInputPort: 'Please input port',
    pleaseInputUsername: 'Please input username',
    pleaseInputPassword: 'Please input password',
    pleaseSelectKeychain: 'Please select keychain',
    pleaseInputAlias: 'Please input alias',
    pleaseSelectGroup: 'Please select group',
    personal: 'Personal',
    enterprise: 'Enterprise',
    editHost: 'Edit Host',
    saveAsset: 'Save',
    createAsset: 'Create',
    deleteConfirm: 'Delete Confirmation',
    deleteConfirmContent: 'Are you sure you want to delete asset "{name}"?',
    deleteSuccess: 'Asset {name} deleted successfully',
    deleteFailure: 'Delete failed',
    deleteError: 'Delete error: {error}',
    createSuccess: 'Asset created successfully',
    saveSuccess: 'Save successful',
    saveError: 'Save error',
    favoriteUpdateSuccess: 'Asset {name} favorite status updated',
    favoriteUpdateFailure: 'Failed to update favorite status',
    favoriteUpdateError: 'Error updating favorite status',
    defaultGroup: 'Hosts',
    hostType: 'ssh',
    personalAsset: 'Personal Asset',
    enterpriseAsset: 'Enterprise Asset',
    organizationTip: 'Connect to enterprise assets via Jumpserver',
    refreshAssets: 'Refresh Assets',
    refreshingAssets: 'Refreshing assets...',
    refreshSuccess: 'Assets refreshed successfully',
    refreshError: 'Failed to refresh assets',
    validationRemoteHostRequired: 'Remote host cannot be empty',
    validationPortRequired: 'Port cannot be empty',
    validationUsernameRequired: 'Username cannot be empty',
    validationPasswordRequired: 'Password cannot be empty',
    validationKeychainRequired: 'Keychain cannot be empty',
    validationEnterpriseRequiredFields: 'Enterprise asset remote host, port, username, password or keychain cannot be empty'
  },
  ai: {
    welcome: 'What can I do for you in the terminal?',
    loginPrompt: 'Login to use AI features, new users can use for free for two weeks',
    searchHost: 'Search by IP',
    noMatchingHosts: 'No matching hosts',
    copy: 'Copy',
    run: 'Run',
    reject: 'Reject',
    cancel: 'Cancel',
    resume: 'Resume',
    agentMessage: 'Command query,troubleshoot errors,handle tasks,anything',
    cmdMessage: 'Work on explicitly opened terminal',
    chatMessage: 'Ask, learn, brainstorm ',
    newChat: 'New Chat',
    showChatHistory: 'Show History',
    closeAiSidebar: 'Close AI Sidebar',
    addHost: 'Add Host',
    processing: 'processing...',
    searchHistoryPH: 'Please Input',
    loading: 'loading...',
    loadMore: 'load more',
    copyToClipboard: 'Copy to clipboard',
    retry: 'Retry',
    taskCompleted: 'Task Completed',
    newTask: 'Start New Task',
    codePreview: 'Code Preview ({lines} lines)'
  },
  keyChain: {
    newKey: 'New Key',
    editKey: 'Edit Key',
    saveKey: 'Save Key',
    keyDrop: 'Drag and drop private key file here to import',
    createKey: 'Create Key',
    deleteConfirm: 'Delete Confirmation',
    deleteConfirmContent: 'Are you sure you want to delete key "{name}"?',
    privateKey: 'Private Key',
    publicKey: 'Public Key',
    passphrase: 'Passphrase',
    alias: 'Alias',
    key: 'Key',
    pleaseInput: 'Please Input',
    name: 'Name',
    type: 'Type: ',
    deleteSuccess: 'Key {name} deleted successfully',
    deleteFailure: 'Delete failed',
    deleteError: 'Delete error: {error}',
    createSuccess: 'Key created successfully',
    saveSuccess: 'Save successful',
    saveError: 'Save error'
  },
  userInfo: {
    enterprise: 'Enterprise User',
    personal: 'Personal User',
    name: 'Name',
    username: 'Username',
    mobile: 'Mobile',
    email: 'Email',
    organization: 'Organization',
    ip: 'IP Address',
    macAddress: 'Mac Address',
    password: 'Password',
    pleaseInputName: 'Please input name',
    pleaseInputUsername: 'Please input username',
    pleaseInputMobile: 'Please input mobile number',
    pleaseInputNewPassword: 'Please input new password',
    nameRequired: 'Name is required',
    nameTooLong: 'Name length cannot exceed 20 characters',
    usernameLengthError: 'Username length must be between 6-20 characters',
    usernameFormatError: 'Username can only contain letters, numbers and underscores',
    mobileInvalid: 'Please enter a valid mobile number',
    passwordLengthError: 'Password length cannot be less than 6 characters',
    passwordStrengthError: 'Password strength must be at least weak',
    passwordStrength: 'Password Strength',
    passwordStrengthWeak: 'Weak',
    passwordStrengthMedium: 'Medium',
    passwordStrengthStrong: 'Strong',
    updateSuccess: 'Update successful',
    updateFailed: 'Update failed',
    passwordResetSuccess: 'Password reset successfully',
    passwordResetFailed: 'Failed to reset password',
    edit: 'Edit',
    save: 'Save',
    cancel: 'Cancel',
    resetPassword: 'Reset Password',
    expirationTime: 'Expiration Time'
  },
  update: {
    available: 'A new version is available',
    update: 'Update',
    later: 'Later',
    downloading: 'Downloading update',
    complete: 'Download complete, install now?',
    install: 'Install'
  },
  files: {
    name: 'Name',
    permissions: 'Permissions',
    size: 'Size',
    modifyDate: 'Modified Date',
    uploadDirectory: 'Upload Directory',
    uploadFile: 'Upload File',
    rollback: 'Back',
    moveTo: 'Move To',
    cpTo: 'Copy To',
    originPath: 'Original Path',
    targetPath: 'Target Path',
    pathInputTips: 'Please enter the target path, e.g., /root/tmp',
    noDirTips: 'No subdirectories available',
    dirEdit: 'Edit',
    conflictTips: 'A file with the same name already exists in the target directory',
    newFileName: 'New File Name',
    rename: 'rename',
    overwrite: 'Overwrite',
    overwriteTips: 'Do you want to overwrite or save with a new name?',
    file: 'File',
    exists: 'already exists in',
    deleteFileTips: 'Are you sure you want to delete the following files?',
    deleting: 'Deleting...',
    deleteSuccess: 'Delete Successful',
    deleteFailed: 'Delete Failed',
    deleteError: 'Delete Error',
    modifySuccess: 'Modification Successful',
    modifyFailed: 'Modification Failed',
    modifyError: 'Modification Error',
    downloading: 'Downloading...',
    downloadSuccess: 'Download Successful',
    downloadFailed: 'Download Failed',
    downloadError: 'Download Error',
    uploading: 'Uploading...',
    uploadSuccess: 'Upload Successful',
    uploadFailed: 'Upload Failed',
    uploadError: 'Upload Error',
    copyFileSuccess: 'File Copied Successfully',
    copyFileFailed: 'File Copy Failed',
    copyFileError: 'File Copy Error',
    moveFileSuccess: 'File Moved Successfully',
    moveFileFailed: 'File Move Failed',
    moveFileError: 'File Move Error',
    modifyFilePermissionsSuccess: 'File Permissions Modified Successfully',
    modifyFilePermissionsFailed: 'Failed to Modify File Permissions',
    modifyFilePermissionsError: 'File Permissions Modification Error',
    read: 'Read',
    write: 'Write',
    exec: 'exec',
    applyToSubdirectories: 'Apply to Subdirectories',
    publicGroup: 'Public Group',
    userGroups: 'User Groups',
    owner: 'Owner',
    permissionSettings: 'Permission Settings',
    delete: 'Delete',
    move: 'Move',
    copy: 'Copy',
    more: 'More',
    download: 'Download',
    doubleClickToOpen: 'Double Click to Open'
  },
  about: {
    version: 'Version',
    checkUpdate: 'Check Update',
    latestVersion: 'Latest Version',
    downLoadUpdate: 'Download Update',
    downloading: 'Downloading',
    checkUpdateError: 'Check update failed',
    checking: 'Checking'
  },
  logs: {
    logs: 'Logs',
    loginLogs: 'Login Logs',
    operationLogs: 'Operation Logs',
    loginLogsDesc: 'View user login records',
    operationLogsDesc: 'View command operation records',
    username: 'Username',
    email: 'Email',
    ipAddress: 'IP Address',
    macAddress: 'MAC Address',
    loginMethod: 'Login Method',
    status: 'Status',
    platform: 'Platform',
    loginTime: 'Login Time',
    loginSuccess: 'Success',
    loginFailed: 'Failed',
    unknown: 'Unknown',
    searchByEmail: 'Search by email',
    startDate: 'Start Date',
    endDate: 'End Date',
    items: 'items',
    fetchError: 'Failed to fetch data',
    reset: 'Reset'
  }
}
